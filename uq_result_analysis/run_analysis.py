#!/usr/bin/env python3
"""
UQ结果分析脚本
运行完整的UQ分析并生成可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 设置图表大小
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 100

def setup_paper_style():
    """设置论文发表风格 - 较大字体版本"""
    plt.rcParams.update({
        'font.size': 14,           # 较大基础字体
        'axes.titlesize': 16,      # 较大标题字体
        'axes.labelsize': 14,      # 较大坐标轴标签字体
        'xtick.labelsize': 12,     # 较大x轴刻度字体
        'ytick.labelsize': 12,     # 较大y轴刻度字体
        'legend.fontsize': 12,     # 较大图例字体
        'figure.titlesize': 18,    # 较大图形标题字体
        'lines.linewidth': 1.5,
        'grid.alpha': 0.4,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_data():
    """加载数据"""
    print("加载数据...")
    data_dir = Path('uq_result_analysis/data')
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')
    
    with open(data_dir / 'data_summary.json', 'r', encoding='utf-8') as f:
        summary = json.load(f)
    
    print(f"数据加载完成: {len(df)} 条记录")
    return df, summary

def preprocess_data(df):
    """数据预处理"""
    print("数据预处理...")

    # 移除uq_value为空的记录
    df_clean = df.dropna(subset=['uq_value']).copy()

    # 过滤掉unknown模型
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()

    print(f"清理后数据量: {len(df_analysis)}")
    print(f"模型数量: {df_analysis['llm_model'].nunique()}")
    print(f"任务数量: {df_analysis['task_name'].nunique()}")
    print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")

    return df_analysis

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理"""
    print("对UQ值进行归一化处理...")

    df_normalized = df_analysis.copy()

    # 为每个UQ方法单独进行归一化
    normalization_stats = {}

    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']

        # 计算归一化参数
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()

        # 使用Min-Max归一化到[0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0  # 如果所有值相同，归一化为0

        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values

        # 同时计算Z-score归一化
        if std_val != 0:
            z_score_values = (method_values - mean_val) / std_val
        else:
            z_score_values = method_values * 0

        df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values

        # 保存归一化统计信息
        normalization_stats[method] = {
            'original_min': min_val,
            'original_max': max_val,
            'original_mean': mean_val,
            'original_std': std_val,
            'range': max_val - min_val
        }

    print("归一化完成")
    print("归一化方法:")
    print("  - uq_value_normalized: Min-Max归一化到[0,1]")
    print("  - uq_value_zscore: Z-score标准化")

    return df_normalized, normalization_stats

def create_individual_model_task_uq_plots(df_analysis, output_dir):
    """为每个model-task-uq method组合创建单独的分布图"""
    print("创建model-task-uq method单独分析图...")

    setup_paper_style()

    # 获取所有组合
    combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    combinations = combinations[combinations['count'] >= 5]  # 只分析样本数>=5的组合

    print(f"发现 {len(combinations)} 个有效的model-task-uq method组合")

    for idx, row in combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']
        method = row['uq_method']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task) &
            (df_analysis['uq_method'] == method)
        ]

        if len(subset) < 5:
            continue

        # 创建三个独立的图表

        # 1. 直方图
        fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
        ax1.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.7,
                color='skyblue', edgecolor='black')
        ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
                   label=f'Mean: {subset["uq_value"].mean():.3f}')
        ax1.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
                   label=f'Median: {subset["uq_value"].median():.3f}')
        ax1.set_xlabel('UQ Value')
        ax1.set_ylabel('Frequency')
        ax1.set_title(f'{model} - {task} - {method}\nDistribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        plt.tight_layout()
        filename1 = f"{model}_{task}_{method}_distribution.pdf"
        plt.savefig(output_dir / filename1, format='pdf', bbox_inches='tight')
        plt.close()

        # # 2. 小提琴图
        # fig2, ax2 = plt.subplots(1, 1, figsize=(8, 10))
        # parts = ax2.violinplot([subset['uq_value']], positions=[1], showmeans=True, showmedians=True)
        # for pc in parts['bodies']:
        #     pc.set_facecolor('lightgreen')
        #     pc.set_alpha(0.7)
        # ax2.set_ylabel('UQ Value')
        # ax2.set_title(f'{model} - {task} - {method}\nViolin Plot')
        # ax2.set_xticks([1])
        # ax2.set_xticklabels([method], rotation=45)
        # ax2.grid(True, alpha=0.3)

        # plt.tight_layout()
        # filename2 = f"{model}_{task}_{method}_violin.pdf"
        # plt.savefig(output_dir / filename2, format='pdf', bbox_inches='tight')
        # plt.close()

        # # 3. 统计信息图表
        # fig3, ax3 = plt.subplots(1, 1, figsize=(10, 8))

        # # 创建统计信息表格
        # stats_data = [
        #     ['Count', f"{len(subset)}"],
        #     ['Mean', f"{subset['uq_value'].mean():.4f}"],
        #     ['Std', f"{subset['uq_value'].std():.4f}"],
        #     ['Variance', f"{subset['uq_value'].var():.4f}"],
        #     ['Median', f"{subset['uq_value'].median():.4f}"],
        #     ['Min', f"{subset['uq_value'].min():.4f}"],
        #     ['Max', f"{subset['uq_value'].max():.4f}"],
        #     ['Q25', f"{subset['uq_value'].quantile(0.25):.4f}"],
        #     ['Q75', f"{subset['uq_value'].quantile(0.75):.4f}"]
        # ]

        # table = ax3.table(cellText=stats_data,
        #                  colLabels=['Statistic', 'Value'],
        #                  cellLoc='center',
        #                  loc='center')
        # table.auto_set_font_size(False)
        # table.set_fontsize(16)
        # table.scale(1.5, 2.0)
        # ax3.axis('off')
        # ax3.set_title(f'{model} - {task} - {method}\nStatistics Summary')

        # plt.tight_layout()
        # filename3 = f"{model}_{task}_{method}_statistics.pdf"
        # plt.savefig(output_dir / filename3, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"单独分析图已保存到: {output_dir}")

def create_model_task_comparison_plots(df_analysis, output_dir):
    """为每个model-task组合创建UQ方法比较图（使用归一化值）"""
    print("创建model-task UQ方法比较图（归一化）...")

    setup_paper_style()

    # 获取model-task组合
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).size().reset_index(name='count')
    model_task_combinations = model_task_combinations[model_task_combinations['count'] >= 50]  # 只分析样本数>=50的组合

    print(f"发现 {len(model_task_combinations)} 个有效的model-task组合")

    for idx, row in model_task_combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task)
        ]

        if len(subset) < 50:
            continue

        # 创建多个独立的图表
        methods = subset['uq_method'].unique()
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))

        # 1. 原始值分布对比
        fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
        for i, method in enumerate(methods):
            method_data = subset[subset['uq_method'] == method]['uq_value']
            ax1.hist(method_data, bins=15, alpha=0.6, label=method, color=colors[i])

        ax1.set_xlabel('Original UQ Value')
        ax1.set_ylabel('Frequency')
        ax1.set_title(f'{model} - {task}\nOriginal Values Distribution')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        plt.tight_layout()
        filename1 = f"{model}_{task}_original_distribution.pdf"
        plt.savefig(output_dir / filename1, format='pdf', bbox_inches='tight')
        plt.close()

        # 2. 原始值小提琴图
        fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
        method_data_orig = [subset[subset['uq_method'] == method]['uq_value'].values for method in methods]
        parts = ax2.violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
        for pc in parts['bodies']:
            pc.set_alpha(0.7)
        ax2.set_title(f'{model} - {task}\nOriginal Values Violin Plot')
        ax2.set_xlabel('UQ Method')
        ax2.set_ylabel('Original UQ Value')
        ax2.set_xticks(range(len(methods)))
        ax2.set_xticklabels(methods, rotation=45)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        filename2 = f"{model}_{task}_original_violin.pdf"
        plt.savefig(output_dir / filename2, format='pdf', bbox_inches='tight')
        plt.close()

        # 3. 归一化值分布对比
        fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
        for i, method in enumerate(methods):
            method_data = subset[subset['uq_method'] == method]['uq_value_normalized']
            ax3.hist(method_data, bins=15, alpha=0.6, label=method, color=colors[i])

        ax3.set_xlabel('Normalized UQ Value [0,1]')
        ax3.set_ylabel('Frequency')
        ax3.set_title(f'{model} - {task}\nNormalized Values Distribution (Min-Max)')
        ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()
        filename3 = f"{model}_{task}_normalized_distribution.pdf"
        plt.savefig(output_dir / filename3, format='pdf', bbox_inches='tight')
        plt.close()

        # 4. 归一化值小提琴图
        fig4, ax4 = plt.subplots(1, 1, figsize=(12, 8))
        method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]
        parts = ax4.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
        for pc in parts['bodies']:
            pc.set_alpha(0.7)
        ax4.set_title(f'{model} - {task}\nNormalized Values Violin Plot')
        ax4.set_xlabel('UQ Method')
        ax4.set_ylabel('Normalized UQ Value [0,1]')
        ax4.set_xticks(range(len(methods)))
        ax4.set_xticklabels(methods, rotation=45)
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        filename4 = f"{model}_{task}_normalized_violin.pdf"
        plt.savefig(output_dir / filename4, format='pdf', bbox_inches='tight')
        plt.close()

        # 5. Z-score值分布对比
        fig5, ax5 = plt.subplots(1, 1, figsize=(12, 8))
        for i, method in enumerate(methods):
            method_data = subset[subset['uq_method'] == method]['uq_value_zscore']
            ax5.hist(method_data, bins=15, alpha=0.6, label=method, color=colors[i])

        ax5.set_xlabel('Z-score UQ Value')
        ax5.set_ylabel('Frequency')
        ax5.set_title(f'{model} - {task}\nZ-score Values Distribution')
        ax5.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax5.grid(True, alpha=0.3)

        plt.tight_layout()
        filename5 = f"{model}_{task}_zscore_distribution.pdf"
        plt.savefig(output_dir / filename5, format='pdf', bbox_inches='tight')
        plt.close()

        # 6. 统计比较表格
        fig6, ax6 = plt.subplots(1, 1, figsize=(14, 8))
        stats_comparison = []
        for method in methods:
            method_subset = subset[subset['uq_method'] == method]
            stats_comparison.append([
                method,
                f"{len(method_subset)}",
                f"{method_subset['uq_value'].mean():.3f}",
                f"{method_subset['uq_value_normalized'].mean():.3f}",
                f"{method_subset['uq_value_zscore'].mean():.3f}",
                f"{method_subset['uq_value_normalized'].std():.3f}"
            ])

        table = ax6.table(cellText=stats_comparison,
                         colLabels=['Method', 'Count', 'Orig Mean', 'Norm Mean', 'Z-score Mean', 'Norm Std'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(14)
        table.scale(1.5, 2.0)
        ax6.axis('off')
        ax6.set_title(f'{model} - {task}\nComparison Statistics')

        plt.tight_layout()
        filename6 = f"{model}_{task}_comparison_statistics.pdf"
        plt.savefig(output_dir / filename6, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"归一化方法比较图已保存到: {output_dir}")

def create_overall_analysis_plots(df_analysis, output_dir):
    """创建整体分析图（分开保存）"""
    print("创建整体分析图...")

    setup_paper_style()

    # 1. 整体分布
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 8))
    ax1.hist(df_analysis['uq_value'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(df_analysis['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
               label=f'Mean: {df_analysis["uq_value"].mean():.3f}')
    ax1.axvline(df_analysis['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
               label=f'Median: {df_analysis["uq_value"].median():.3f}')
    ax1.set_xlabel('UQ Value')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Overall UQ Value Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_distribution.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 2. 按模型分布
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    for model in df_analysis['llm_model'].unique():
        model_data = df_analysis[df_analysis['llm_model'] == model]['uq_value']
        ax2.hist(model_data, bins=30, alpha=0.6, label=model)
    ax2.set_xlabel('UQ Value')
    ax2.set_ylabel('Frequency')
    ax2.set_title('UQ Value Distribution by Model')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'distribution_by_model.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 3. 按任务分布
    fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
    for task in df_analysis['task_name'].unique():
        task_data = df_analysis[df_analysis['task_name'] == task]['uq_value']
        ax3.hist(task_data, bins=30, alpha=0.6, label=task)
    ax3.set_xlabel('UQ Value')
    ax3.set_ylabel('Frequency')
    ax3.set_title('UQ Value Distribution by Task')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'distribution_by_task.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 4. 按UQ方法分布（选择主要方法）
    fig4, ax4 = plt.subplots(1, 1, figsize=(14, 8))
    top_methods = df_analysis['uq_method'].value_counts().head(6).index
    for method in top_methods:
        method_data = df_analysis[df_analysis['uq_method'] == method]['uq_value']
        ax4.hist(method_data, bins=30, alpha=0.6, label=method)
    ax4.set_xlabel('UQ Value')
    ax4.set_ylabel('Frequency')
    ax4.set_title('UQ Value Distribution by Top UQ Methods')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'distribution_by_method.pdf', format='pdf', bbox_inches='tight')
    plt.close()

def create_overall_heatmaps(df_analysis, output_dir):
    """创建整体热力图分析（分开保存）"""
    print("创建整体热力图分析...")

    setup_paper_style()

    # 1. 平均UQ值热力图
    fig1, ax1 = plt.subplots(1, 1, figsize=(10, 8))
    pivot_mean = df_analysis.pivot_table(values='uq_value', index='llm_model',
                                         columns='task_name', aggfunc='mean')
    sns.heatmap(pivot_mean, annot=True, fmt='.3f', cmap='RdYlBu_r',
                ax=ax1, cbar_kws={'label': 'Mean UQ Value'})
    ax1.set_title('Mean UQ Value by Model and Task')
    ax1.set_xlabel('Task')
    ax1.set_ylabel('Model')

    plt.tight_layout()
    plt.savefig(output_dir / 'heatmap_mean_uq_value.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 2. 标准差热力图
    fig2, ax2 = plt.subplots(1, 1, figsize=(10, 8))
    pivot_std = df_analysis.pivot_table(values='uq_value', index='llm_model',
                                       columns='task_name', aggfunc='std')
    sns.heatmap(pivot_std, annot=True, fmt='.3f', cmap='Reds',
                ax=ax2, cbar_kws={'label': 'Std UQ Value'})
    ax2.set_title('UQ Value Standard Deviation by Model and Task')
    ax2.set_xlabel('Task')
    ax2.set_ylabel('Model')

    plt.tight_layout()
    plt.savefig(output_dir / 'heatmap_std_uq_value.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 3. 样本数量热力图
    fig3, ax3 = plt.subplots(1, 1, figsize=(10, 8))
    pivot_count = df_analysis.pivot_table(values='uq_value', index='llm_model',
                                         columns='task_name', aggfunc='count')
    sns.heatmap(pivot_count, annot=True, fmt='.0f', cmap='Blues',
                ax=ax3, cbar_kws={'label': 'Sample Count'})
    ax3.set_title('Sample Count by Model and Task')
    ax3.set_xlabel('Task')
    ax3.set_ylabel('Model')

    plt.tight_layout()
    plt.savefig(output_dir / 'heatmap_sample_count.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 4. 按模型和方法的平均UQ值热力图
    fig4, ax4 = plt.subplots(1, 1, figsize=(14, 8))
    pivot_model_method = df_analysis.pivot_table(values='uq_value', index='llm_model',
                                                 columns='uq_method', aggfunc='mean')
    sns.heatmap(pivot_model_method, annot=True, fmt='.3f', cmap='viridis',
                ax=ax4, cbar_kws={'label': 'Mean UQ Value'})
    ax4.set_title('Mean UQ Value by Model and Method')
    ax4.set_xlabel('UQ Method')
    ax4.set_ylabel('Model')

    plt.tight_layout()
    plt.savefig(output_dir / 'heatmap_model_method.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 5. 按任务和方法的平均UQ值热力图
    fig5, ax5 = plt.subplots(1, 1, figsize=(14, 8))
    pivot_task_method = df_analysis.pivot_table(values='uq_value', index='task_name',
                                               columns='uq_method', aggfunc='mean')
    sns.heatmap(pivot_task_method, annot=True, fmt='.3f', cmap='plasma',
                ax=ax5, cbar_kws={'label': 'Mean UQ Value'})
    ax5.set_title('Mean UQ Value by Task and Method')
    ax5.set_xlabel('UQ Method')
    ax5.set_ylabel('Task')

    plt.tight_layout()
    plt.savefig(output_dir / 'heatmap_task_method.pdf', format='pdf', bbox_inches='tight')
    plt.close()

def create_overall_method_comparison(df_analysis, output_dir):
    """创建整体UQ方法比较图（原始值vs归一化值）"""
    print("创建整体UQ方法比较图（归一化）...")

    setup_paper_style()

    # 计算原始值和归一化值的统计
    method_stats_orig = df_analysis.groupby('uq_method')['uq_value'].agg([
        'count', 'mean', 'std', 'median', 'min', 'max'
    ]).round(4)

    method_stats_norm = df_analysis.groupby('uq_method')['uq_value_normalized'].agg([
        'count', 'mean', 'std', 'median', 'min', 'max'
    ]).round(4)

    # 创建多个独立的对比图
    methods = df_analysis['uq_method'].unique()

    # 1. 原始值小提琴图
    fig1, ax1 = plt.subplots(1, 1, figsize=(14, 8))
    method_data_orig = [df_analysis[df_analysis['uq_method'] == method]['uq_value'].values for method in methods]
    parts = ax1.violinplot(method_data_orig, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax1.set_title('Overall Original Values Distribution')
    ax1.set_xlabel('UQ Method')
    ax1.set_ylabel('Original UQ Value')
    ax1.set_xticks(range(len(methods)))
    ax1.set_xticklabels(methods, rotation=45)
    ax1.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_original_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 2. 原始值平均值条形图
    fig2, ax2 = plt.subplots(1, 1, figsize=(12, 8))
    method_means_orig = method_stats_orig['mean'].sort_values(ascending=True)
    ax2.barh(range(len(method_means_orig)), method_means_orig.values,
            color=plt.cm.viridis(np.linspace(0, 1, len(method_means_orig))))
    ax2.set_yticks(range(len(method_means_orig)))
    ax2.set_yticklabels(method_means_orig.index)
    ax2.set_xlabel('Mean Original UQ Value')
    ax2.set_title('Mean Original Values by Method')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_original_means.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 3. 归一化值小提琴图
    fig3, ax3 = plt.subplots(1, 1, figsize=(14, 8))
    method_data_norm = [df_analysis[df_analysis['uq_method'] == method]['uq_value_normalized'].values for method in methods]
    parts = ax3.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax3.set_title('Overall Normalized Values Distribution [0,1]')
    ax3.set_xlabel('UQ Method')
    ax3.set_ylabel('Normalized UQ Value')
    ax3.set_xticks(range(len(methods)))
    ax3.set_xticklabels(methods, rotation=45)
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_normalized_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 4. 归一化值平均值条形图
    fig4, ax4 = plt.subplots(1, 1, figsize=(12, 8))
    method_means_norm = method_stats_norm['mean'].sort_values(ascending=True)
    ax4.barh(range(len(method_means_norm)), method_means_norm.values,
            color=plt.cm.plasma(np.linspace(0, 1, len(method_means_norm))))
    ax4.set_yticks(range(len(method_means_norm)))
    ax4.set_yticklabels(method_means_norm.index)
    ax4.set_xlabel('Mean Normalized UQ Value')
    ax4.set_title('Mean Normalized Values by Method')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_normalized_means.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 5. Z-score值小提琴图
    fig5, ax5 = plt.subplots(1, 1, figsize=(14, 8))
    method_data_zscore = [df_analysis[df_analysis['uq_method'] == method]['uq_value_zscore'].values for method in methods]
    parts = ax5.violinplot(method_data_zscore, positions=range(len(methods)), showmeans=True, showmedians=True)
    for pc in parts['bodies']:
        pc.set_alpha(0.7)
    ax5.set_title('Overall Z-score Values Distribution')
    ax5.set_xlabel('UQ Method')
    ax5.set_ylabel('Z-score UQ Value')
    ax5.set_xticks(range(len(methods)))
    ax5.set_xticklabels(methods, rotation=45)
    ax5.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_zscore_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    # 6. 归一化效果比较表格
    fig6, ax6 = plt.subplots(1, 1, figsize=(16, 10))
    comparison_data = []
    for method in methods:
        orig_stats = method_stats_orig.loc[method]
        norm_stats = method_stats_norm.loc[method]
        comparison_data.append([
            method,
            f"{orig_stats['count']}",
            f"{orig_stats['mean']:.3f}",
            f"{norm_stats['mean']:.3f}",
            f"{orig_stats['std']:.3f}",
            f"{norm_stats['std']:.3f}",
            f"{orig_stats['std']/orig_stats['mean']:.3f}" if orig_stats['mean'] != 0 else "N/A"
        ])

    table = ax6.table(cellText=comparison_data,
                     colLabels=['Method', 'Count', 'Orig Mean', 'Norm Mean', 'Orig Std', 'Norm Std', 'Orig CV'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(14)
    table.scale(1.5, 2.0)
    ax6.axis('off')
    ax6.set_title('Overall Normalization Comparison')

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_normalization_comparison.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    return method_stats_orig, method_stats_norm

def generate_statistics(df_analysis, output_dir):
    """生成统计数据"""
    print("生成统计数据...")
    
    # 按模型、任务、方法分组统计
    grouped_stats = df_analysis.groupby(['llm_model', 'task_name', 'uq_method'])['uq_value'].agg([
        'count', 'mean', 'std', 'var', 'median', 'min', 'max'
    ]).round(4)
    
    # 保存详细统计结果
    grouped_stats.to_csv(output_dir / 'grouped_statistics.csv')
    
    # 模型-任务统计
    model_task_stats = df_analysis.groupby(['llm_model', 'task_name'])['uq_value'].agg([
        'mean', 'std', 'count'
    ]).round(4)
    
    return grouped_stats, model_task_stats

def generate_report(df_analysis, output_dir):
    """生成分析报告"""
    print("生成分析报告...")
    
    model_performance = df_analysis.groupby('llm_model')['uq_value'].agg(['mean', 'std', 'count']).round(4)
    task_difficulty = df_analysis.groupby('task_name')['uq_value'].agg(['mean', 'std', 'count']).round(4)
    method_effectiveness = df_analysis.groupby('uq_method')['uq_value'].agg(['mean', 'std', 'count']).round(4)
    
    report = {
        'data_overview': {
            'total_samples': len(df_analysis),
            'num_models': df_analysis['llm_model'].nunique(),
            'num_tasks': df_analysis['task_name'].nunique(),
            'num_methods': df_analysis['uq_method'].nunique()
        },
        'uq_value_stats': {
            'mean': float(df_analysis['uq_value'].mean()),
            'std': float(df_analysis['uq_value'].std()),
            'median': float(df_analysis['uq_value'].median()),
            'min': float(df_analysis['uq_value'].min()),
            'max': float(df_analysis['uq_value'].max())
        },
        'model_performance': model_performance.to_dict('index'),
        'task_difficulty': task_difficulty.to_dict('index'),
        'method_effectiveness': method_effectiveness.to_dict('index')
    }
    
    with open(output_dir / 'analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report

def main():
    """主函数 - 按照新的分析流程执行（包含归一化处理）"""
    print("=== UQ结果分析开始 ===")
    print("分析流程: 1) 数据归一化 -> 2) 单独model-task-uq分析 -> 3) model-task合并比较 -> 4) 整体分析")

    # 创建输出目录
    output_dir = Path('uq_result_analysis/data')
    figures_dir = Path('uq_result_analysis/figures')
    figures_dir.mkdir(parents=True, exist_ok=True)

    # 加载和预处理数据
    df, summary = load_data()
    df_analysis = preprocess_data(df)

    print("\n=== 数据归一化处理 ===")
    # 对UQ值进行归一化处理
    df_analysis, normalization_stats = normalize_uq_values(df_analysis)

    # 保存归一化统计信息
    import json
    with open(output_dir / 'normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)
    print(f"归一化统计信息已保存到: {output_dir / 'normalization_stats.json'}")

    print("\n=== 第一步: 单独model-task-uq method分析 ===")
    # 1. 为每个model-task-uq method组合创建单独的分析图
    create_individual_model_task_uq_plots(df_analysis, figures_dir)

    print("\n=== 第二步: model-task合并比较分析（归一化） ===")
    # 2. 为每个model-task组合创建UQ方法比较图（使用归一化值）
    create_model_task_comparison_plots(df_analysis, figures_dir)

    print("\n=== 第三步: 整体分析（归一化） ===")
    # 3. 创建整体分析图
    create_overall_analysis_plots(df_analysis, figures_dir)
    create_overall_heatmaps(df_analysis, figures_dir)
    method_stats_orig, method_stats_norm = create_overall_method_comparison(df_analysis, figures_dir)

    print("\n=== 生成统计数据和报告 ===")
    # 生成统计数据（包含归一化值）
    grouped_stats, model_task_stats = generate_statistics(df_analysis, output_dir)

    # 生成报告（包含归一化信息）
    report = generate_report(df_analysis, output_dir)

    # 添加归一化统计到报告
    report['normalization_info'] = {
        'methods_normalized': list(normalization_stats.keys()),
        'normalization_types': ['min_max_0_1', 'z_score'],
        'original_ranges': {method: stats['range'] for method, stats in normalization_stats.items()}
    }

    # 重新保存包含归一化信息的报告
    with open(output_dir / 'analysis_report_with_normalization.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    # 打印摘要
    print("\n=== 分析完成 ===")
    print(f"总样本数: {report['data_overview']['total_samples']}")
    print(f"模型数量: {report['data_overview']['num_models']}")
    print(f"任务数量: {report['data_overview']['num_tasks']}")
    print(f"UQ方法数量: {report['data_overview']['num_methods']}")
    print(f"\n原始UQ值统计:")
    print(f"  均值: {report['uq_value_stats']['mean']:.4f}")
    print(f"  标准差: {report['uq_value_stats']['std']:.4f}")
    print(f"  范围: [{report['uq_value_stats']['min']:.4f}, {report['uq_value_stats']['max']:.4f}]")

    print(f"\n归一化处理:")
    print(f"  已对 {len(normalization_stats)} 个UQ方法进行归一化")
    print(f"  归一化类型: Min-Max [0,1] 和 Z-score")

    print(f"\n所有PDF图表已保存到: {figures_dir}")
    print(f"统计数据已保存到: {output_dir}")

    # 列出生成的文件
    pdf_files = list(figures_dir.glob("*.pdf"))
    print(f"\n生成的PDF文件 ({len(pdf_files)} 个):")
    for pdf_file in sorted(pdf_files):
        print(f"  - {pdf_file.name}")

    print(f"\n重要说明:")
    print(f"  - 所有UQ方法比较图都使用了归一化值，确保不同方法间的可比性")
    print(f"  - 归一化统计信息保存在: normalization_stats.json")
    print(f"  - 完整分析报告保存在: analysis_report_with_normalization.json")

if __name__ == "__main__":
    main()
