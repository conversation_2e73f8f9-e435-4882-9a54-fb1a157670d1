#!/usr/bin/env python3
"""
UQ结果分析脚本
运行完整的UQ分析并生成可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 设置图表大小
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 100

def setup_paper_style():
    """设置论文发表风格 - 较大字体版本"""
    plt.rcParams.update({
        'font.size': 14,           # 较大基础字体
        'axes.titlesize': 16,      # 较大标题字体
        'axes.labelsize': 14,      # 较大坐标轴标签字体
        'xtick.labelsize': 12,     # 较大x轴刻度字体
        'ytick.labelsize': 12,     # 较大y轴刻度字体
        'legend.fontsize': 12,     # 较大图例字体
        'figure.titlesize': 18,    # 较大图形标题字体
        'lines.linewidth': 1.5,
        'grid.alpha': 0.4,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

def load_data():
    """加载数据"""
    print("加载数据...")
    data_dir = Path('data')  # 修改路径，因为我们已经在uq_result_analysis目录下
    df = pd.read_csv(data_dir / 'combined_uq_results.csv')

    with open(data_dir / 'data_summary.json', 'r', encoding='utf-8') as f:
        summary = json.load(f)

    print(f"数据加载完成: {len(df)} 条记录")
    return df, summary

def preprocess_data(df):
    """数据预处理"""
    print("数据预处理...")

    # 移除uq_value为空的记录
    df_clean = df.dropna(subset=['uq_value']).copy()

    # 过滤掉unknown模型
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()

    print(f"清理后数据量: {len(df_analysis)}")
    print(f"模型数量: {df_analysis['llm_model'].nunique()}")
    print(f"任务数量: {df_analysis['task_name'].nunique()}")
    print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")

    return df_analysis

def normalize_uq_values(df_analysis):
    """对每个UQ方法的值进行归一化处理"""
    print("对UQ值进行归一化处理...")

    df_normalized = df_analysis.copy()

    # 为每个UQ方法单独进行归一化
    normalization_stats = {}

    for method in df_analysis['uq_method'].unique():
        method_mask = df_analysis['uq_method'] == method
        method_values = df_analysis.loc[method_mask, 'uq_value']

        # 计算归一化参数
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()

        # 使用Min-Max归一化到[0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0  # 如果所有值相同，归一化为0

        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values

        # 同时计算Z-score归一化
        if std_val != 0:
            z_score_values = (method_values - mean_val) / std_val
        else:
            z_score_values = method_values * 0

        df_normalized.loc[method_mask, 'uq_value_zscore'] = z_score_values

        # 保存归一化统计信息
        normalization_stats[method] = {
            'original_min': min_val,
            'original_max': max_val,
            'original_mean': mean_val,
            'original_std': std_val,
            'range': max_val - min_val
        }

    print("归一化完成")
    print("归一化方法:")
    print("  - uq_value_normalized: Min-Max归一化到[0,1]")
    print("  - uq_value_zscore: Z-score标准化")

    return df_normalized, normalization_stats

def create_individual_model_task_uq_plots(df_analysis, output_dir):
    """为每个model-task-uq method组合创建单独的分布图（数值分布+核密度）"""
    print("创建model-task-uq method单独分析图...")

    setup_paper_style()

    # 获取所有组合
    combinations = df_analysis.groupby(['llm_model', 'task_name', 'uq_method']).size().reset_index(name='count')
    combinations = combinations[combinations['count'] >= 5]  # 只分析样本数>=5的组合

    print(f"发现 {len(combinations)} 个有效的model-task-uq method组合")

    for idx, row in combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']
        method = row['uq_method']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task) &
            (df_analysis['uq_method'] == method)
        ]

        if len(subset) < 5:
            continue

        # 创建分布图（直方图 + 核密度估计）
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 上图：直方图
        ax1.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.7,
                color='skyblue', edgecolor='black', density=True)
        ax1.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
                   label=f'Mean: {subset["uq_value"].mean():.3f}')
        ax1.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
                   label=f'Median: {subset["uq_value"].median():.3f}')
        ax1.set_xlabel('UQ Value')
        ax1.set_ylabel('Density')
        ax1.set_title(f'{model} - {task} - {method}\nHistogram Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 下图：核密度估计
        try:
            from scipy.stats import gaussian_kde
            if len(subset['uq_value'].unique()) > 1:  # 确保有足够的变异性
                kde = gaussian_kde(subset['uq_value'])
                x_range = np.linspace(subset['uq_value'].min(), subset['uq_value'].max(), 200)
                ax2.plot(x_range, kde(x_range), 'b-', linewidth=2, label='KDE')
                ax2.fill_between(x_range, kde(x_range), alpha=0.3, color='lightblue')
            else:
                ax2.text(0.5, 0.5, 'Insufficient variation for KDE',
                        transform=ax2.transAxes, ha='center', va='center')
        except ImportError:
            # 如果没有scipy，使用简单的平滑曲线
            ax2.hist(subset['uq_value'], bins=min(20, len(subset)//2), alpha=0.7,
                    color='lightgreen', edgecolor='black', density=True)

        ax2.axvline(subset['uq_value'].mean(), color='red', linestyle='--', linewidth=3,
                   label=f'Mean: {subset["uq_value"].mean():.3f}')
        ax2.axvline(subset['uq_value'].median(), color='orange', linestyle='--', linewidth=3,
                   label=f'Median: {subset["uq_value"].median():.3f}')
        ax2.set_xlabel('UQ Value')
        ax2.set_ylabel('Density')
        ax2.set_title(f'{model} - {task} - {method}\nKernel Density Estimation')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f"{model}_{task}_{method}_distribution.pdf"
        plt.savefig(output_dir / filename, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"单独分析图已保存到: {output_dir}")

def create_model_task_comparison_plots(df_analysis, output_dir):
    """为每个model-task组合创建UQ方法比较图（只使用归一化后的violin图）"""
    print("创建model-task UQ方法比较图（归一化violin图）...")

    setup_paper_style()

    # 获取model-task组合，且该组合下有多个UQ方法
    model_task_combinations = df_analysis.groupby(['llm_model', 'task_name']).agg({
        'uq_method': 'nunique',
        'uq_value': 'count'
    }).reset_index()
    model_task_combinations.columns = ['llm_model', 'task_name', 'num_methods', 'total_count']

    # 只分析有多个UQ方法且样本数>=50的组合
    model_task_combinations = model_task_combinations[
        (model_task_combinations['num_methods'] > 1) &
        (model_task_combinations['total_count'] >= 50)
    ]

    print(f"发现 {len(model_task_combinations)} 个有效的model-task组合（多UQ方法）")

    for idx, row in model_task_combinations.iterrows():
        model = row['llm_model']
        task = row['task_name']

        # 提取数据
        subset = df_analysis[
            (df_analysis['llm_model'] == model) &
            (df_analysis['task_name'] == task)
        ]

        if len(subset) < 50:
            continue

        methods = subset['uq_method'].unique()
        if len(methods) <= 1:  # 跳过只有一个方法的组合
            continue

        # 只创建归一化值小提琴图
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        method_data_norm = [subset[subset['uq_method'] == method]['uq_value_normalized'].values for method in methods]

        try:
            parts = ax.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
            if 'bodies' in parts:
                for pc in parts['bodies']:
                    pc.set_alpha(0.7)
        except Exception as e:
            print(f"Warning: 无法为 {model}-{task} 创建violin图: {e}")
            plt.close()
            continue

        ax.set_title(f'{model} - {task}\nNormalized UQ Values Comparison')
        ax.set_xlabel('UQ Method')
        ax.set_ylabel('Normalized UQ Value [0,1]')
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f"{model}_{task}_normalized_violin.pdf"
        plt.savefig(output_dir / filename, format='pdf', bbox_inches='tight')
        plt.close()

    print(f"归一化方法比较图已保存到: {output_dir}")

# 删除整体分析图 - 不需要

# 删除热力图分析 - 不需要

def create_overall_method_comparison(df_analysis, output_dir):
    """创建整体UQ方法比较图（只保留归一化值小提琴图）"""
    print("创建整体UQ方法比较图（归一化）...")

    setup_paper_style()

    # 计算归一化值的统计
    method_stats_norm = df_analysis.groupby('uq_method')['uq_value_normalized'].agg([
        'count', 'mean', 'std', 'median', 'min', 'max'
    ]).round(4)

    # 创建归一化值小提琴图
    methods = df_analysis['uq_method'].unique()

    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    method_data_norm = [df_analysis[df_analysis['uq_method'] == method]['uq_value_normalized'].values for method in methods]

    try:
        parts = ax.violinplot(method_data_norm, positions=range(len(methods)), showmeans=True, showmedians=True)
        if 'bodies' in parts:
            for pc in parts['bodies']:
                pc.set_alpha(0.7)
    except Exception as e:
        print(f"Warning: 无法创建整体violin图: {e}")
        plt.close()
        return

    ax.set_title('Overall Normalized UQ Values Distribution [0,1]')
    ax.set_xlabel('UQ Method')
    ax.set_ylabel('Normalized UQ Value')
    ax.set_xticks(range(len(methods)))
    ax.set_xticklabels(methods, rotation=45, ha='right')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_dir / 'overall_normalized_violin.pdf', format='pdf', bbox_inches='tight')
    plt.close()

    print(f"整体归一化方法比较图已保存到: {output_dir}")

def generate_comprehensive_statistics(df_analysis, output_dir):
    """生成完整的UQ统计信息CSV文件"""
    print("生成完整的UQ统计信息...")

    # 创建完整的统计信息表
    stats_list = []

    # 按模型、任务、方法分组统计
    for (model, task, method), group in df_analysis.groupby(['llm_model', 'task_name', 'uq_method']):
        if len(group) < 5:  # 跳过样本数太少的组合
            continue

        # 原始值统计
        orig_stats = group['uq_value'].describe()

        # 归一化值统计
        norm_stats = group['uq_value_normalized'].describe()

        # Z-score统计
        zscore_stats = group['uq_value_zscore'].describe()

        # 组合统计信息
        stats_row = {
            'llm_model': model,
            'task_name': task,
            'uq_method': method,
            'sample_count': len(group),

            # 原始值统计
            'orig_mean': round(orig_stats['mean'], 4),
            'orig_std': round(orig_stats['std'], 4),
            'orig_var': round(group['uq_value'].var(), 4),
            'orig_median': round(orig_stats['50%'], 4),
            'orig_min': round(orig_stats['min'], 4),
            'orig_max': round(orig_stats['max'], 4),
            'orig_q25': round(orig_stats['25%'], 4),
            'orig_q75': round(orig_stats['75%'], 4),
            'orig_range': round(orig_stats['max'] - orig_stats['min'], 4),
            'orig_cv': round(orig_stats['std'] / orig_stats['mean'], 4) if orig_stats['mean'] != 0 else 0,

            # 归一化值统计
            'norm_mean': round(norm_stats['mean'], 4),
            'norm_std': round(norm_stats['std'], 4),
            'norm_var': round(group['uq_value_normalized'].var(), 4),
            'norm_median': round(norm_stats['50%'], 4),
            'norm_min': round(norm_stats['min'], 4),
            'norm_max': round(norm_stats['max'], 4),
            'norm_q25': round(norm_stats['25%'], 4),
            'norm_q75': round(norm_stats['75%'], 4),
            'norm_range': round(norm_stats['max'] - norm_stats['min'], 4),

            # Z-score统计
            'zscore_mean': round(zscore_stats['mean'], 4),
            'zscore_std': round(zscore_stats['std'], 4),
            'zscore_var': round(group['uq_value_zscore'].var(), 4),
            'zscore_median': round(zscore_stats['50%'], 4),
            'zscore_min': round(zscore_stats['min'], 4),
            'zscore_max': round(zscore_stats['max'], 4),
            'zscore_q25': round(zscore_stats['25%'], 4),
            'zscore_q75': round(zscore_stats['75%'], 4),
            'zscore_range': round(zscore_stats['max'] - zscore_stats['min'], 4),
        }

        stats_list.append(stats_row)

    # 转换为DataFrame并保存
    comprehensive_stats_df = pd.DataFrame(stats_list)
    comprehensive_stats_df = comprehensive_stats_df.sort_values(['llm_model', 'task_name', 'uq_method'])

    # 保存完整统计信息
    comprehensive_stats_df.to_csv(output_dir / 'comprehensive_uq_statistics.csv', index=False)

    print(f"完整UQ统计信息已保存到: {output_dir / 'comprehensive_uq_statistics.csv'}")
    print(f"统计信息包含 {len(comprehensive_stats_df)} 个model-task-method组合")

    return comprehensive_stats_df

# 删除generate_report函数 - 不需要

def main():
    """主函数 - 按照新的分析流程执行（包含归一化处理）"""
    print("=== UQ结果分析开始 ===")
    print("分析流程: 1) 数据归一化 -> 2) 单独model-task-uq分析 -> 3) model-task合并比较 -> 4) 整体分析")

    # 创建输出目录
    output_dir = Path('data')  # 修改路径
    figures_dir = Path('figures')  # 修改路径
    figures_dir.mkdir(parents=True, exist_ok=True)

    # 加载和预处理数据
    df, summary = load_data()
    df_analysis = preprocess_data(df)

    print("\n=== 数据归一化处理 ===")
    # 对UQ值进行归一化处理
    df_analysis, normalization_stats = normalize_uq_values(df_analysis)

    # 保存归一化统计信息
    import json
    with open(output_dir / 'normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)
    print(f"归一化统计信息已保存到: {output_dir / 'normalization_stats.json'}")

    print("\n=== 第一步: 单独model-task-uq method分析 ===")
    # 1. 为每个model-task-uq method组合创建单独的分析图
    create_individual_model_task_uq_plots(df_analysis, figures_dir)

    print("\n=== 第二步: model-task合并比较分析（归一化） ===")
    # 2. 为每个model-task组合创建UQ方法比较图（使用归一化值）
    create_model_task_comparison_plots(df_analysis, figures_dir)

    print("\n=== 第三步: 整体分析（归一化） ===")
    # 只保留整体归一化方法比较
    create_overall_method_comparison(df_analysis, figures_dir)

    print("\n=== 生成统计数据 ===")
    # 生成完整的UQ统计信息CSV文件
    comprehensive_stats = generate_comprehensive_statistics(df_analysis, output_dir)

    # 保存归一化统计信息
    with open(output_dir / 'normalization_stats.json', 'w', encoding='utf-8') as f:
        json.dump(normalization_stats, f, indent=2, ensure_ascii=False)

    # 打印摘要
    print("\n=== 分析完成 ===")
    print(f"总样本数: {len(df_analysis)}")
    print(f"模型数量: {df_analysis['llm_model'].nunique()}")
    print(f"任务数量: {df_analysis['task_name'].nunique()}")
    print(f"UQ方法数量: {df_analysis['uq_method'].nunique()}")
    print(f"\n原始UQ值统计:")
    print(f"  均值: {df_analysis['uq_value'].mean():.4f}")
    print(f"  标准差: {df_analysis['uq_value'].std():.4f}")
    print(f"  范围: [{df_analysis['uq_value'].min():.4f}, {df_analysis['uq_value'].max():.4f}]")

    print(f"\n归一化处理:")
    print(f"  已对 {len(normalization_stats)} 个UQ方法进行归一化")
    print(f"  归一化类型: Min-Max [0,1] 和 Z-score")

    print(f"\n所有PDF图表已保存到: {figures_dir}")
    print(f"统计数据已保存到: {output_dir}")

    # 列出生成的文件
    pdf_files = list(figures_dir.glob("*.pdf"))
    print(f"\n生成的PDF文件 ({len(pdf_files)} 个):")
    for pdf_file in sorted(pdf_files):
        print(f"  - {pdf_file.name}")

    print(f"\n重要文件:")
    print(f"  - 完整UQ统计信息: comprehensive_uq_statistics.csv")
    print(f"  - 归一化统计信息: normalization_stats.json")

    print(f"\n分析说明:")
    print(f"  - 单一model-task-uq组合: 只生成分布图（直方图+核密度）")
    print(f"  - 多UQ方法的model-task组合: 只生成归一化后的violin图对比")
    print(f"  - 整体分析: 生成归一化后的整体violin图")

if __name__ == "__main__":
    main()
