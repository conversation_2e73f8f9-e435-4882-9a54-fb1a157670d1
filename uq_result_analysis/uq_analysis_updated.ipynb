# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def setup_paper_style():
    """设置论文发表风格 - 大字体版本"""
    plt.rcParams.update({
        'font.size': 20,           # 增大基础字体
        'axes.titlesize': 20,      # 增大标题字体
        'axes.labelsize': 18,      # 增大坐标轴标签字体
        'xtick.labelsize': 18,     # 增大x轴刻度字体
        'ytick.labelsize': 18,     # 增大y轴刻度字体
        'legend.fontsize': 18,     # 增大图例字体
        'figure.titlesize': 22,    # 增大图形标题字体
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.spines.top': False,
        'axes.spines.right': False,
    })

# 应用大字体设置
setup_paper_style()

# 创建输出目录
figures_dir = Path('figures')
figures_dir.mkdir(parents=True, exist_ok=True)

print("环境设置完成 - 大字体论文风格")
print(f"图表将保存到: {figures_dir}")
print("字体设置:")
print("  - 基础字体: 16pt")
print("  - 标题字体: 20pt")
print("  - 坐标轴标签: 18pt")
print("  - 刻度标签: 16pt")
print("  - 图例字体: 14pt")

df_analysis = pd.read_csv('./uq_result_analysis/data/combined_uq_results.csv')
df_analysis

# 基于 (llm_model, task_name) 分组的UQ统计
group_cols = ['llm_model', 'task_name','uq_method']

def compute_group_stats(df, value_col, suffix=""):
    stats = (
        df.groupby(group_cols)[value_col]
          .agg(count='count',
               mean='mean',
               median='median',
               std='std',
               Q25=lambda x: x.quantile(0.25),
               Q75=lambda x: x.quantile(0.75),
               min='min',
               max='max')
          .reset_index()
    )
    if suffix:
        rename_map = {c: f"{c}{suffix}" for c in stats.columns if c not in group_cols and c != 'count'}
        # count 不重复添加后缀（保持一致）
        stats = stats.rename(columns=rename_map)
    return stats

uq_stats = compute_group_stats(df_analysis, 'uq_value', suffix="")
if 'uq_value_normalized' in df_analysis.columns:
    uq_norm_stats = compute_group_stats(df_analysis, 'uq_value_normalized', suffix="_norm")
    # 合并（count 相同，仅保留一份）
    merged_stats = uq_stats.merge(uq_norm_stats, on=group_cols, how='left')
else:
    merged_stats = uq_stats

# 排序
merged_stats = merged_stats.sort_values(group_cols).reset_index(drop=True)

# 保存
out_csv = figures_dir / 'uq_group_stats.csv'
merged_stats.to_csv(out_csv, index=False)
print(f"已生成分组统计并保存到: {out_csv}")
merged_stats



# 改进：柱状图显示样本数量（Count，左轴），KDE 显示密度（Density，右轴）
# 为每个 (llm_model, task_name, uq_method) 输出原始 & 归一化双坐标分布图

# 若缺失归一化列且相关函数存在则尝试补齐
if 'uq_value_normalized' not in df_analysis.columns:
    if 'compute_normalization_stats' in globals() and 'apply_manual_normalization' in globals():
        normalization_stats = compute_normalization_stats(df_analysis, 'uq_value')
        apply_manual_normalization(df_analysis, normalization_stats)
    else:
        print("缺少归一化列且未找到归一化函数，跳过归一化。")

plt.rcParams.update({
    "figure.dpi": 120,
    "axes.titlesize": 18,
    "axes.labelsize": 16,
    "xtick.labelsize": 13,
    "ytick.labelsize": 13,
    "legend.fontsize": 12,
    "font.family": "sans-serif"
})

def _has_variation(data):
    return (len(data) >= 3) and (data.std() > 0)

def _plot_hist_with_density(values, x_label, title, outfile, color_hist, color_kde,
                            xlim=None):
    fig, ax_count = plt.subplots(figsize=(5.2, 4.4))
    # Histogram -> count
    sns.histplot(values, bins='auto', stat='count', element='step',
                 fill=False, color=color_hist, ax=ax_count, linewidth=1.6)
    ax_count.set_xlabel(x_label)
    ax_count.set_ylabel("Count")
    ax_count.grid(alpha=0.25, linestyle='--', linewidth=0.6)
    ax_count.set_axisbelow(True)

    # Density (secondary y-axis)
    ax_density = ax_count.twinx()
    if _has_variation(values):
        sns.kdeplot(values, ax=ax_density, color=color_kde, lw=2)
        ax_density.set_ylabel("Density")
    else:
        ax_density.set_yticks([])
        ax_density.set_ylabel("Density (N/A)")
    # 主题 & n
    ax_count.set_title(title)
    ax_count.text(0.98, 0.92, f"n={len(values)}",
                  transform=ax_count.transAxes, ha='right', va='top', fontsize=12)
    if xlim:
        ax_count.set_xlim(*xlim)
    fig.tight_layout()
    fig.savefig(outfile, format='pdf', bbox_inches='tight')
    plt.close(fig)

def plot_single_method_distributions(sub_df, model, task, method):
    if sub_df.empty:
        return
    base_tag = f"{model}_{task}_{method}".replace('/', '_')

    # 原始值
    raw_vals = sub_df['uq_value'].dropna()
    _plot_hist_with_density(
        raw_vals,
        x_label="UQ Value (Raw)",
        title=f"{model} | {task}\n{method} (Raw)",
        outfile=figures_dir / f"dist_raw_{base_tag}.pdf",
        color_hist="#1f77b4",
        color_kde="#d62728",
        xlim=None
    )

    # 归一化值（若存在）
    if 'uq_value_normalized' in sub_df.columns:
        norm_vals = sub_df['uq_value_normalized'].dropna()
        _plot_hist_with_density(
            norm_vals,
            x_label="UQ Value (Normalized)",
            title=f"{model} | {task}\n{method} (Normalized)",
            outfile=figures_dir / f"dist_norm_{base_tag}.pdf",
            color_hist="#2ca02c",
            color_kde="#ff7f0e",
            xlim=(-0.05, 1.05)
        )

def generate_all_method_distributions_dual_axis():
    triples = (df_analysis[['llm_model', 'task_name', 'uq_method']]
               .drop_duplicates()
               .sort_values(['llm_model', 'task_name', 'uq_method']))
    total = len(triples)
    print(f"将生成 {total * (2 if 'uq_value_normalized' in df_analysis.columns else 1)} 张双轴图 ...")
    for _, row in triples.iterrows():
        plot_single_method_distributions(
            df_analysis[
                (df_analysis.llm_model == row.llm_model) &
                (df_analysis.task_name == row.task_name) &
                (df_analysis.uq_method == row.uq_method)
            ],
            row.llm_model, row.task_name, row.uq_method
        )
    print("全部双轴分布图生成完成。")

generate_all_method_distributions_dual_axis()


from matplotlib.ticker import AutoMinorLocator

# 优化：去掉 n=xxx；添加更清晰的网格（主/次刻度）；统一字体
def plot_methods_distribution_dual_panels(
    df, model, task,
    raw_col='uq_value',
    norm_col='uq_value_normalized',
    rotate=90,
    annotate_mean=True,
    mean_fmt="{:.3g}",
    mean_color="#d62728",
    font_family="DejaVu Sans",
    font_size=11,
    minor_grid=True
):
    sub = df[(df.llm_model == model) & (df.task_name == task)].copy()
    if sub.empty:
        return
    has_norm = norm_col in sub.columns
    cols = ['uq_method', raw_col] + ([norm_col] if has_norm else [])
    sub = sub[cols].dropna(subset=[raw_col])

    method_order = (
        sub.groupby('uq_method')[raw_col]
           .median()
           .sort_values(ascending=False)
           .index.tolist()
    )

    # 记录旧 rc
    old_rc = {
        'font.family': plt.rcParams.get('font.family'),
        'font.size': plt.rcParams.get('font.size'),
        'axes.titlesize': plt.rcParams.get('axes.titlesize'),
        'axes.labelsize': plt.rcParams.get('axes.labelsize'),
        'xtick.labelsize': plt.rcParams.get('xtick.labelsize'),
        'ytick.labelsize': plt.rcParams.get('ytick.labelsize')
    }
    plt.rcParams.update({
        'font.family': font_family,
        'font.size': font_size,
        'axes.titlesize': font_size + 4,
        'axes.labelsize': font_size + 1,
        'xtick.labelsize': font_size - 1,
        'ytick.labelsize': font_size - 1
    })

    n_panels = 2 if has_norm else 1
    width = max(8, 0.5 * len(method_order)) * n_panels
    fig, axes = plt.subplots(1, n_panels, figsize=(width, 6), sharey=False)
    if n_panels == 1:
        axes = [axes]

    violin_kwargs = dict(inner='box', cut=0, scale='width')
    text_kwargs = dict(ha='center', va='bottom', fontsize=font_size - 2, color=mean_color)

    # 面板 1：Raw
    ax_raw = axes[0]
    sns.violinplot(
        data=sub,
        x='uq_method',
        y=raw_col,
        order=method_order,
        ax=ax_raw,
        **violin_kwargs
    )
    ax_raw.set_xlabel("UQ Method")
    ax_raw.set_ylabel("UQ Value (Raw)")
    ax_raw.set_title(f"{model} | {task} (Raw)")
    ax_raw.tick_params(axis='x', rotation=rotate)

    ax_raw.yaxis.set_minor_locator(AutoMinorLocator(2))
    ax_raw.grid(alpha=0.3, linestyle='--', linewidth=0.7)
    if minor_grid:
        ax_raw.grid(alpha=0.15, linestyle=':', linewidth=0.5, which='minor')

    raw_means = sub.groupby('uq_method')[raw_col].mean()
    if annotate_mean:
        for i, m in enumerate(method_order):
            mean_val = raw_means.get(m)
            if pd.notna(mean_val):
                ax_raw.scatter(i, mean_val, color=mean_color, s=28, zorder=4, edgecolor='white', linewidth=0.6)
                ax_raw.text(i, mean_val, mean_fmt.format(mean_val), **text_kwargs)

    # 面板 2：Normalized
    if has_norm:
        sub_norm = sub.dropna(subset=[norm_col])
        ax_norm = axes[1]
        sns.violinplot(
            data=sub_norm,
            x='uq_method',
            y=norm_col,
            order=method_order,
            ax=ax_norm,
            palette="pastel",
            **violin_kwargs
        )
        ax_norm.set_xlabel("UQ Method")
        ax_norm.set_ylabel("UQ Value (Normalized)")
        ax_norm.set_title(f"{model} | {task} (Normalized)")
        ax_norm.tick_params(axis='x', rotation=rotate)

        ax_norm.yaxis.set_minor_locator(AutoMinorLocator(2))
        ax_norm.grid(alpha=0.3, linestyle='--', linewidth=0.7)
        if minor_grid:
            ax_norm.grid(alpha=0.15, linestyle=':', linewidth=0.5, which='minor')

        norm_means = sub_norm.groupby('uq_method')[norm_col].mean()
        if annotate_mean:
            for i, m in enumerate(method_order):
                mean_val = norm_means.get(m)
                if pd.notna(mean_val):
                    ax_norm.scatter(i, mean_val, color=mean_color, s=28, zorder=4, edgecolor='white', linewidth=0.6)
                    ax_norm.text(i, mean_val, mean_fmt.format(mean_val), **text_kwargs)

    fig.suptitle(
        f"{model} | {task}  (Raw vs Normalized)" if has_norm else f"{model} | {task} (Raw Only)",
        y=1.02
    )
    fig.tight_layout()
    out_name = f"panel_dual_raw_norm_{model}_{task}.pdf".replace('/', '_')
    fig.savefig(figures_dir / out_name, format='pdf', bbox_inches='tight')
    plt.close(fig)

    # 还原 rc
    plt.rcParams.update(old_rc)


def plot_all_model_task_dual_panels(df):
    combos = df[['llm_model', 'task_name']].drop_duplicates().sort_values(['llm_model', 'task_name'])
    has_norm = 'uq_value_normalized' in df.columns
    print(f"将生成 {len(combos)} 张图；包含归一化列: {has_norm}")
    for _, row in combos.iterrows():
        plot_methods_distribution_dual_panels(df, row.llm_model, row.task_name)
    print("Raw & Normalized 双面板方法分布图完成。")

plot_all_model_task_dual_panels(df_analysis)




# 为每个 (llm_model, task_name) 绘制所有 uq_method 的分布（同一面板）
# 若存在归一化列则优先使用 uq_value_normalized，否则使用原始 uq_value

def plot_methods_distribution_single_panel(df, model, task, value_col):
    sub = df[(df.llm_model == model) & (df.task_name == task)].copy()
    if sub.empty:
        return
    # 仅保留需要列
    sub = sub[['uq_method', value_col]].dropna()
    # 根据中位数排序，便于比较
    method_order = (
        sub.groupby('uq_method')[value_col]
           .median()
           .sort_values(ascending=False)
           .index.tolist()
    )
    width = max(10, 0.55 * len(method_order))
    fig, ax = plt.subplots(figsize=(width, 6))
    sns.violinplot(
        data=sub,
        x='uq_method',
        y=value_col,
        order=method_order,
        inner='box',
        cut=0,
        scale='width'
    )
    ax.set_xlabel("UQ Method")
    ax.set_ylabel("UQ Value" + (" (Normalized)" if value_col.endswith("normalized") else ""))
    ax.set_title(f"{model} | {task}")
    ax.tick_params(axis='x', rotation=25)
    ax.grid(alpha=0.25, linestyle='--', axis='y')
    # 添加样本数标注
    counts = sub.groupby('uq_method')[value_col].count()
    for i, m in enumerate(method_order):
        ax.text(i, ax.get_ylim()[0], f"n={counts.get(m,0)}",
                ha='center', va='bottom', rotation=90, fontsize=12, color='#444')
    fig.tight_layout()
    out_name = f"panel_dist_{value_col}_{model}_{task}.pdf".replace('/', '_')
    fig.savefig(figures_dir / out_name, format='pdf', bbox_inches='tight')
    plt.close(fig)

def plot_all_model_task_panels(df):
    value_col = 'uq_value_normalized' if 'uq_value_normalized' in df.columns else 'uq_value'
    combos = df[['llm_model', 'task_name']].drop_duplicates().sort_values(['llm_model','task_name'])
    print(f"使用列: {value_col}；将生成 {len(combos)} 个面板图。")
    for _, row in combos.iterrows():
        plot_methods_distribution_single_panel(df, row.llm_model, row.task_name, value_col)
    print("全部面板分布图已生成。")

plot_all_model_task_panels(df_analysis)

# 示例：分析第一个model-task组合
if len(valid_mt_combinations) > 0:
    print("=== 示例：分析第一个model-task组合 ===")
    
    row = valid_mt_combinations.iloc[0]
    model = row['llm_model']
    task = row['task_name']
    
    # 提取数据
    subset = df_analysis[
        (df_analysis['llm_model'] == model) & 
        (df_analysis['task_name'] == task)
    ]
    
    create_model_task_comparison(model, task, subset, save_plots=True)
else:
    print("没有找到有效的model-task组合")

# 3. 按任务分布（密度曲线）
fig3, ax3 = plt.subplots(1, 1, figsize=(12, 8))
for task in df_analysis['task_name'].unique():
    task_data = df_analysis[df_analysis['task_name'] == task]['uq_value']
    # 绘制密度曲线
    task_data.plot.density(ax=ax3, alpha=0.7, label=task, linewidth=3)
ax3.set_xlabel('UQ Value')
ax3.set_ylabel('Density')
ax3.set_title('UQ Value Density Distribution by Task')
ax3.legend()
ax3.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig(figures_dir / 'distribution_by_task.pdf', format='pdf', bbox_inches='tight')
plt.show()
plt.close()